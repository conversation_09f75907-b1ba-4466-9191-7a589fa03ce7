#ifndef EKF_IMU_ODOMETRY_HPP  
#define EKF_IMU_ODOMETRY_HPP  

#include <Eigen/Dense>
#include <iostream>
using namespace Eigen;
class ekf_imu_odometry
{
private:
    /* data */
public:
    ekf_imu_odometry(/* args */);
    ~ekf_imu_odometry();
    void predict( const Eigen::Vector2f &u_now );  
    void update( const double z );
    Eigen::Vector3f getState();
private:
    // state vector at k-1 moment, ( x, y, theta )
    Eigen:: Vector3f x_pre = Vector3f::Zero();
    // state vector at k moment, ( x, y, theta )
    Eigen::Vector3f x_now = Vector3f::Zero();
    // state covarince matrix
    Eigen::Matrix3f P_pre = Matrix3f::Identity();
    Eigen::Matrix3f P_now = Matrix3f::Identity();
    // state Jacobian matrix
    Eigen::Matrix3f F = Matrix3f::Identity();
    // measurement update matrix
    // Eigen::Matrix<float,1,3> H = MatrixXd::Zero(1,3);
    Eigen::Matrix<float,1,3> H ;
    // state Gaussian Noise
    Eigen::Matrix3f Q = Matrix3f::Identity() ;
    // measurement Gaussian Noise
    double R = 5;
    bool is_init = false;
};



#endif