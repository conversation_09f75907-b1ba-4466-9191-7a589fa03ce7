#include <vector>
#include <ros/ros.h>
#include <param.h>
#include <sensor_msgs/Imu.h>
#include <nav_msgs/Odometry.h>
#include <common_msgs/SpeedStreer.h>
#include <ekf_imu_odometry.hpp>
#include <perception_msgs/PerceptionLocalization.h>
#include <control_msgs/Jinlong_Control_ModeFlag.h>
#include <control_msgs/VehicleFdb.h>
#include "geometry_msgs/PoseStamped.h"
#include <cmath>
#include <iostream>
#include <Eigen/Dense>
typedef Eigen::Matrix<double, 6, 1> Vector6d;
class wheelOdometry
{
private:
    /* data */
public:
    wheelOdometry(/* args */);
    ~wheelOdometry();
    Eigen::Matrix4d convertToEngineMatrix(double x, double y, double z, double roll, double pitch, double yaw);
    Vector6d convertFromEngineMatrix(Eigen::Matrix4d transform);
    /*imu数据回调*/
    void imu_callback(const perception_msgs::PerceptionLocalization::ConstPtr& input);
    void fusion_callback(const perception_msgs::PerceptionLocalization::ConstPtr& input);
    void gear_callback(const control_msgs::Jinlong_Control_ModeFlag::ConstPtr& input);

    // EKF参数调整方法
    void setEKFNoiseParameters(double pos_noise, double angle_noise, double imu_noise);
    void resetEKF(const Eigen::Vector3f& initial_state);
    /*轮速回调函数*/
    // void wheel_callback(const control_msgs::VehicleFdb::ConstPtr& input);
    void wheel_callback(const common_msgs::SpeedStreer::ConstPtr& input);
    void run();
private:
    int current_gear = 0;
    ros::Publisher wheel_odom_pub;
    ros::Publisher wheel_odom_pub_;
    ros::Publisher gnss_odom_pub;
    ekf_imu_odometry m_ekf_imu_odometry;
    Eigen::Vector2f m_wheelData;  // 轮速数据向量 [位移增量, 角度增量]
    float angle_delta_z;
    double rtk_x;
    double rtk_y;
    double VehicleWheelWidth = 1.7;
    double last_time = 0.0;  // 上一次时间戳
    float last_y = 0.0;      // 上一次y坐标
    Eigen::Matrix4d origin_gnss_pose;
    Eigen::Matrix4d pose_now = Eigen::Matrix4d::Identity();
    Eigen::Matrix4d pose_last = Eigen::Matrix4d::Identity();
};


