#include <iostream>
#include <memory>
#include "../include/ekf_imu_odometry.hpp"

// 简化的EKF运行时测试，不依赖ROS
class SimpleEKFTester {
private:
    ekf_imu_odometry ekf;
    double last_time;
    
public:
    SimpleEKFTester() : last_time(0.0) {
        std::cout << "\n=== 简化EKF测试器初始化 ===" << std::endl;
        
        // 初始化EKF
        Eigen::Vector3f initial_state(0.0f, 0.0f, 0.0f);
        ekf.initialize(initial_state);
        ekf.setProcessNoise(0.1, 0.01);
        ekf.setMeasurementNoise(0.05);
    }
    
    void simulateWheelData(double speed_left, double speed_right, double timestamp, double imu_angular_velocity = 0.0) {
        std::cout << "\n--- 模拟轮速数据 ---" << std::endl;
        std::cout << "时间戳: " << timestamp << " s" << std::endl;
        std::cout << "左轮速度: " << speed_left << " km/h" << std::endl;
        std::cout << "右轮速度: " << speed_right << " km/h" << std::endl;
        std::cout << "IMU角速度: " << imu_angular_velocity << " deg/s" << std::endl;
        
        // 计算时间间隔
        if (last_time <= 0) {
            last_time = timestamp;
            std::cout << "首次调用，初始化时间戳" << std::endl;
            return;
        }
        
        double dt = timestamp - last_time;
        std::cout << "时间间隔: " << dt << " s" << std::endl;
        
        // 防止异常时间间隔
        if (dt <= 0 || dt > 1.0) {
            std::cout << "⚠️ 异常时间间隔，使用默认值 0.01s" << std::endl;
            dt = 0.01;
        }
        
        // 计算控制输入
        double avg_speed = (speed_left + speed_right) * 0.5;  // km/h
        double speed_diff = speed_right - speed_left;  // km/h
        double vehicle_width = 1.7;  // m
        
        // 转换为SI单位
        double displacement = avg_speed / 3.6 * dt;  // m
        double angular_increment = (speed_diff / vehicle_width) / 3.6 * dt;  // rad
        
        std::cout << "控制输入计算:" << std::endl;
        std::cout << "  平均速度: " << avg_speed << " km/h = " << avg_speed/3.6 << " m/s" << std::endl;
        std::cout << "  位移增量: " << displacement << " m" << std::endl;
        std::cout << "  角度增量: " << angular_increment << " rad = " << angular_increment*180/3.14159 << " deg" << std::endl;
        
        // EKF预测
        Eigen::Vector2f control_input(displacement, angular_increment);
        ekf.predict(control_input, dt);
        
        // IMU观测更新
        double imu_angle_increment = imu_angular_velocity * 3.14159/180 * dt;  // 转换为rad
        std::cout << "IMU角度增量: " << imu_angle_increment << " rad = " << imu_angle_increment*180/3.14159 << " deg" << std::endl;
        
        ekf.update(imu_angle_increment);
        
        // 获取结果
        Eigen::Vector3f state = ekf.getState();
        std::cout << "EKF输出状态:" << std::endl;
        std::cout << "  位置: x=" << state(0) << " m, y=" << state(1) << " m" << std::endl;
        std::cout << "  方向: theta=" << state(2) << " rad = " << state(2)*180/3.14159 << " deg" << std::endl;
        
        last_time = timestamp;
        std::cout << "--- 处理完成 ---\n" << std::endl;
    }
    
    void testStraightLine() {
        std::cout << "\n🚗 测试场景1: 直线行驶" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        // 5秒直线行驶，10 km/h
        for (int i = 0; i < 6; i++) {
            simulateWheelData(10.0, 10.0, i * 1.0, 0.0);
        }
    }
    
    void testTurning() {
        std::cout << "\n🔄 测试场景2: 右转弯" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        last_time = 0;  // 重置时间
        
        // 右转：左轮快，右轮慢，IMU检测到角速度
        for (int i = 0; i < 6; i++) {
            simulateWheelData(12.0, 8.0, i * 1.0, 5.0);  // 5 deg/s角速度
        }
    }
    
    void testMixedScenario() {
        std::cout << "\n🌀 测试场景3: 混合场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        last_time = 0;  // 重置时间
        
        // 加速直行
        simulateWheelData(5.0, 5.0, 1.0, 0.0);
        simulateWheelData(10.0, 10.0, 2.0, 0.0);
        simulateWheelData(15.0, 15.0, 3.0, 0.0);
        
        // 转弯
        simulateWheelData(15.0, 10.0, 4.0, 3.0);
        simulateWheelData(15.0, 10.0, 5.0, 3.0);
        
        // 减速直行
        simulateWheelData(10.0, 10.0, 6.0, 0.0);
        simulateWheelData(5.0, 5.0, 7.0, 0.0);
        simulateWheelData(0.0, 0.0, 8.0, 0.0);  // 停车
    }
    
    void testTimeIssues() {
        std::cout << "\n⏰ 测试场景4: 时间处理问题" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        last_time = 0;  // 重置时间
        
        // 正常时间间隔
        simulateWheelData(10.0, 10.0, 1.0, 0.0);
        
        // 大时间间隔（应该被接受）
        simulateWheelData(10.0, 10.0, 2.5, 0.0);  // 1.5秒间隔
        
        // 异常大时间间隔（应该被拒绝）
        simulateWheelData(10.0, 10.0, 5.0, 0.0);  // 2.5秒间隔
        
        // 负时间间隔（应该被拒绝）
        simulateWheelData(10.0, 10.0, 4.0, 0.0);  // 时间倒退
        
        // 零时间间隔（应该被拒绝）
        simulateWheelData(10.0, 10.0, 4.0, 0.0);  // 相同时间
    }
    
    void runAllTests() {
        std::cout << "\n🚀 开始简化EKF运行时测试" << std::endl;
        std::cout << "目标：通过调试信息分析EKF行为和发现问题" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        try {
            testStraightLine();
            testTurning();
            testMixedScenario();
            testTimeIssues();
            
            std::cout << "\n✅ 所有测试场景完成！" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "\n❌ 测试过程中发生错误: " << e.what() << std::endl;
        }
    }
};

int main() {
    std::cout << "简化EKF运行时测试程序" << std::endl;
    std::cout << "不依赖ROS，专注于EKF算法验证" << std::endl;
    
    SimpleEKFTester tester;
    tester.runAllTests();
    
    std::cout << "\n📋 测试总结：" << std::endl;
    std::cout << "1. 检查时间间隔处理是否正确" << std::endl;
    std::cout << "2. 验证控制输入计算是否合理" << std::endl;
    std::cout << "3. 观察EKF状态更新是否符合预期" << std::endl;
    std::cout << "4. 确认角度增量观测模型是否正确" << std::endl;
    
    return 0;
}
