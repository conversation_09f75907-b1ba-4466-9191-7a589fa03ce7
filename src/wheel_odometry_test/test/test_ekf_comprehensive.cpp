#include <iostream>
#include <vector>
#include <cmath>
#include "../include/ekf_imu_odometry.hpp"

// 简单的测试函数
void test_ekf_basic_functionality() {
    std::cout << "\n=== 测试EKF基本功能 ===" << std::endl;
    
    ekf_imu_odometry ekf;
    
    // 测试初始化
    Eigen::Vector3f initial_state(0.0f, 0.0f, 0.0f);  // x=0, y=0, theta=0
    ekf.initialize(initial_state);
    
    std::cout << "初始化状态: " << ekf.isInitialized() << std::endl;
    
    // 设置噪声参数
    ekf.setProcessNoise(0.1, 0.01);  // 位置噪声0.1m^2, 角度噪声0.01rad^2
    ekf.setMeasurementNoise(0.05);   // IMU噪声0.05rad^2
    
    // 模拟车辆直线运动
    std::cout << "\n--- 模拟直线运动 ---" << std::endl;
    for (int i = 0; i < 5; i++) {
        Eigen::Vector2f control(1.0f, 0.0f);  // 1m位移，0角度变化
        double dt = 0.1;  // 0.1秒时间步长
        
        ekf.predict(control, dt);
        
        // 模拟IMU观测（无角度变化）
        double imu_measurement = 0.0;
        ekf.update(imu_measurement);
        
        Eigen::Vector3f state = ekf.getState();
        std::cout << "步骤 " << i+1 << ": x=" << state(0) 
                  << ", y=" << state(1) << ", theta=" << state(2) << std::endl;
    }
    
    // 模拟转弯运动
    std::cout << "\n--- 模拟转弯运动 ---" << std::endl;
    for (int i = 0; i < 5; i++) {
        Eigen::Vector2f control(1.0f, 0.1f);  // 1m位移，0.1rad角度变化
        double dt = 0.1;
        
        ekf.predict(control, dt);
        
        // 模拟IMU观测（有角度变化）
        double imu_measurement = 0.1;  // 与控制输入一致的角度变化
        ekf.update(imu_measurement);
        
        Eigen::Vector3f state = ekf.getState();
        std::cout << "步骤 " << i+1 << ": x=" << state(0) 
                  << ", y=" << state(1) << ", theta=" << state(2) << std::endl;
    }
}

void test_debug_output() {
    std::cout << "\n=== 测试调试输出 ===" << std::endl;
    
    ekf_imu_odometry ekf;
    Eigen::Vector3f initial_state(1.0f, 2.0f, 0.5f);
    ekf.initialize(initial_state);
    
    // 设置噪声参数（会触发调试输出）
    ekf.setProcessNoise(0.2, 0.02);
    ekf.setMeasurementNoise(0.1);
    
    // 执行一次预测和更新（会显示详细调试信息）
    Eigen::Vector2f control(0.5f, 0.05f);
    ekf.predict(control, 0.05);
    ekf.update(0.05);
    
    std::cout << "调试输出测试完成" << std::endl;
}

void test_error_conditions() {
    std::cout << "\n=== 测试错误条件处理 ===" << std::endl;
    
    ekf_imu_odometry ekf;
    
    // 测试未初始化时的行为
    std::cout << "未初始化状态: " << ekf.isInitialized() << std::endl;
    
    Eigen::Vector2f control(1.0f, 0.1f);
    ekf.predict(control, 0.1);  // 应该跳过
    ekf.update(0.1);  // 应该跳过
    
    // 初始化后再测试
    Eigen::Vector3f initial_state(0.0f, 0.0f, 0.0f);
    ekf.initialize(initial_state);
    std::cout << "初始化后状态: " << ekf.isInitialized() << std::endl;
    
    // 现在应该正常工作
    ekf.predict(control, 0.1);
    ekf.update(0.1);
    
    Eigen::Vector3f final_state = ekf.getState();
    std::cout << "最终状态: x=" << final_state(0) 
              << ", y=" << final_state(1) << ", theta=" << final_state(2) << std::endl;
}

void test_mathematical_correctness() {
    std::cout << "\n=== 测试数学正确性 ===" << std::endl;
    
    ekf_imu_odometry ekf;
    Eigen::Vector3f initial_state(0.0f, 0.0f, 0.0f);
    ekf.initialize(initial_state);
    ekf.setProcessNoise(0.01, 0.001);  // 很小的噪声
    ekf.setMeasurementNoise(0.001);
    
    // 测试纯直线运动
    std::cout << "纯直线运动测试:" << std::endl;
    for (int i = 0; i < 3; i++) {
        Eigen::Vector2f control(1.0f, 0.0f);  // 1m直线运动
        ekf.predict(control, 0.1);
        ekf.update(0.0);  // 无角度变化
        
        Eigen::Vector3f state = ekf.getState();
        std::cout << "  步骤 " << i+1 << ": x=" << state(0) 
                  << " (期望=" << (i+1) << "), y=" << state(1) 
                  << " (期望=0), theta=" << state(2) << " (期望=0)" << std::endl;
    }
    
    // 重置并测试纯旋转
    ekf.initialize(Eigen::Vector3f(0.0f, 0.0f, 0.0f));
    std::cout << "\n纯旋转测试:" << std::endl;
    for (int i = 0; i < 3; i++) {
        Eigen::Vector2f control(0.0f, 0.1f);  // 纯旋转0.1rad
        ekf.predict(control, 0.1);
        ekf.update(0.1);  // IMU测量到0.1rad变化
        
        Eigen::Vector3f state = ekf.getState();
        std::cout << "  步骤 " << i+1 << ": x=" << state(0) 
                  << " (期望=0), y=" << state(1) 
                  << " (期望=0), theta=" << state(2) 
                  << " (期望=" << (i+1)*0.1 << ")" << std::endl;
    }
}

int main() {
    std::cout << "开始测试修复后的EKF实现..." << std::endl;
    std::cout << "注意: 调试信息已启用，可以通过注释 EKF_DEBUG_ENABLED 来关闭" << std::endl;
    
    try {
        test_ekf_basic_functionality();
        test_debug_output();
        test_error_conditions();
        test_mathematical_correctness();
        
        std::cout << "\n所有测试完成！" << std::endl;
        std::cout << "\n要关闭调试信息，请在 ekf_imu_odometry.hpp 中注释掉 #define EKF_DEBUG_ENABLED" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
