cmake_minimum_required(VERSION 2.8.3)

# 添加EKF基础测试
add_executable(test_ekf_comprehensive
    test_ekf_comprehensive.cpp
    ../src/ekf_imu_odometry.cpp
)

target_include_directories(test_ekf_comprehensive PRIVATE
    ../include
    ${catkin_INCLUDE_DIRS}
)

target_link_libraries(test_ekf_comprehensive
    ${catkin_LIBRARIES}
)

set_target_properties(test_ekf_comprehensive PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 添加完整性测试
add_executable(test_wheel_odometry_completeness
    test_wheel_odometry_completeness.cpp
    ../src/wheel_odometry.cpp
    ../src/ekf_imu_odometry.cpp
)

target_include_directories(test_wheel_odometry_completeness PRIVATE
    ../include
    ${catkin_INCLUDE_DIRS}
)

target_link_libraries(test_wheel_odometry_completeness
    ${catkin_LIBRARIES}
)

set_target_properties(test_wheel_odometry_completeness PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
