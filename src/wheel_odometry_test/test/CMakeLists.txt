cmake_minimum_required(VERSION 2.8.3)

# 添加测试可执行文件
add_executable(test_ekf_comprehensive 
    test_ekf_comprehensive.cpp
    ../src/ekf_imu_odometry.cpp
)

# 包含头文件目录
target_include_directories(test_ekf_comprehensive PRIVATE
    ../include
    ${catkin_INCLUDE_DIRS}
)

# 链接库
target_link_libraries(test_ekf_comprehensive 
    ${catkin_LIBRARIES}
)

# 设置编译标志
set_target_properties(test_ekf_comprehensive PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)
