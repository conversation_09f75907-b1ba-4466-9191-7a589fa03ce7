#include <iostream>
#include <memory>
#include <ros/ros.h>
#include "../include/wheel_odometry.h"
#include <common_msgs/SpeedStreer.h>

// 模拟运行时测试程序
class WheelOdometryTester {
private:
    wheelOdometry wheel_odom;
    
public:
    WheelOdometryTester() {
        std::cout << "\n=== 初始化WheelOdometry测试器 ===" << std::endl;
    }
    
    void simulateSpeedStreerMessage(double speed_left, double speed_right, double steering_angle, double timestamp) {
        std::cout << "\n--- 模拟SpeedStreer消息 ---" << std::endl;
        std::cout << "时间戳: " << timestamp << std::endl;
        std::cout << "左轮速度: " << speed_left << " km/h" << std::endl;
        std::cout << "右轮速度: " << speed_right << " km/h" << std::endl;
        std::cout << "转向角: " << steering_angle << " deg" << std::endl;
        
        // 创建模拟消息
        common_msgs::SpeedStreer::Ptr msg(new common_msgs::SpeedStreer);
        msg->header.stamp = ros::Time(timestamp);
        msg->rel_speed_rear_axle_left = speed_left;
        msg->rel_speed_rear_axle_right = speed_right;
        msg->strg_angle_real_value = steering_angle;
        
        // 调用回调函数
        wheel_odom.wheel_callback(msg);
        
        std::cout << "--- 消息处理完成 ---\n" << std::endl;
    }
    
    void testStraightLine() {
        std::cout << "\n🚗 测试直线行驶场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        double base_time = 1000.0;  // 基础时间戳
        
        // 模拟5秒的直线行驶，每秒10km/h
        for (int i = 0; i < 5; i++) {
            simulateSpeedStreerMessage(
                10.0,  // 左轮速度 10 km/h
                10.0,  // 右轮速度 10 km/h  
                0.0,   // 无转向
                base_time + i * 1.0  // 每秒一个数据点
            );
        }
    }
    
    void testTurning() {
        std::cout << "\n🔄 测试转弯场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        double base_time = 2000.0;  // 新的基础时间戳
        
        // 模拟右转弯：右轮慢，左轮快
        for (int i = 0; i < 5; i++) {
            simulateSpeedStreerMessage(
                12.0,  // 左轮速度 12 km/h (外侧轮)
                8.0,   // 右轮速度 8 km/h (内侧轮)
                15.0,  // 转向角 15度
                base_time + i * 1.0
            );
        }
    }
    
    void testReverse() {
        std::cout << "\n⬅️ 测试倒车场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        // 首先设置倒档
        control_msgs::Jinlong_Control_ModeFlag::Ptr gear_msg(new control_msgs::Jinlong_Control_ModeFlag);
        gear_msg->vehicle_current_gear = 1;  // 倒档
        wheel_odom.gear_callback(gear_msg);
        
        double base_time = 3000.0;
        
        // 模拟倒车
        for (int i = 0; i < 3; i++) {
            simulateSpeedStreerMessage(
                5.0,   // 左轮速度 5 km/h
                5.0,   // 右轮速度 5 km/h
                0.0,   // 无转向
                base_time + i * 1.0
            );
        }
        
        // 恢复前进档
        gear_msg->vehicle_current_gear = 0;  // 前进档
        wheel_odom.gear_callback(gear_msg);
    }
    
    void testStopAndGo() {
        std::cout << "\n⏸️ 测试停车和启动场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        double base_time = 4000.0;
        
        // 停车
        simulateSpeedStreerMessage(0.0, 0.0, 0.0, base_time);
        simulateSpeedStreerMessage(0.0, 0.0, 0.0, base_time + 1.0);
        
        // 重新启动
        simulateSpeedStreerMessage(5.0, 5.0, 0.0, base_time + 2.0);
        simulateSpeedStreerMessage(10.0, 10.0, 0.0, base_time + 3.0);
    }
    
    void testAbnormalData() {
        std::cout << "\n⚠️ 测试异常数据场景" << std::endl;
        std::cout << std::string(50, '=') << std::endl;
        
        double base_time = 5000.0;
        
        // 异常大的时间间隔
        simulateSpeedStreerMessage(10.0, 10.0, 0.0, base_time);
        simulateSpeedStreerMessage(10.0, 10.0, 0.0, base_time + 0.5);  // 0.5秒间隔，应该被限制
        
        // 异常小的时间间隔
        simulateSpeedStreerMessage(10.0, 10.0, 0.0, base_time + 0.5001);  // 0.0001秒间隔
        
        // 负时间间隔
        simulateSpeedStreerMessage(10.0, 10.0, 0.0, base_time + 0.4);  // 时间倒退
    }
    
    void runAllTests() {
        std::cout << "\n🚀 开始运行WheelOdometry实时测试" << std::endl;
        std::cout << "注意：调试信息已启用，观察EKF内部状态" << std::endl;
        std::cout << std::string(80, '=') << std::endl;
        
        try {
            testStraightLine();
            testTurning();
            testReverse();
            testStopAndGo();
            testAbnormalData();
            
            std::cout << "\n✅ 所有测试场景完成！" << std::endl;
            std::cout << "\n📊 请检查上述调试信息，查看是否存在以下问题：" << std::endl;
            std::cout << "   1. 时间处理是否正确" << std::endl;
            std::cout << "   2. 速度计算是否合理" << std::endl;
            std::cout << "   3. 角度增量是否正确" << std::endl;
            std::cout << "   4. EKF状态更新是否正常" << std::endl;
            std::cout << "   5. 异常数据处理是否健壮" << std::endl;
            
        } catch (const std::exception& e) {
            std::cerr << "\n❌ 测试过程中发生错误: " << e.what() << std::endl;
        }
    }
};

int main(int argc, char** argv) {
    // 初始化ROS（如果需要）
    ros::init(argc, argv, "wheel_odometry_tester");
    
    std::cout << "WheelOdometry运行时测试程序" << std::endl;
    std::cout << "目标：通过调试信息发现潜在问题" << std::endl;
    
    WheelOdometryTester tester;
    tester.runAllTests();
    
    return 0;
}
