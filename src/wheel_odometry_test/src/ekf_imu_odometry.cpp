#include "ekf_imu_odometry.hpp"  

ekf_imu_odometry::ekf_imu_odometry(/* args */)
{
}

ekf_imu_odometry::~ekf_imu_odometry()
{
}
Eigen::Vector3f  ekf_imu_odometry::getState()
{
    return x_now;
}
void ekf_imu_odometry::predict( const Vector2f &u_now )  
{
         if( is_init == false ){
               is_init = true;
               return;
         }
        //  std::cout<<" start predict "<<std::endl;

         // 1. state prediction
         x_now(0) = x_pre(0) + u_now(0) * ::cos( x_pre(2) + 0.5 * u_now(1) ); // x
         x_now(1) = x_pre(1) + u_now(0) * ::sin( x_pre(2) + 0.5 * u_now(1) ); // y
         x_now(2) = x_pre(2) + u_now(1); // theta

         // 2. caculate state Jacobian matrix    
         F = Matrix3f::Identity();
         F(0, 2) = -u_now(0) * ::sin( x_pre(2) + 0.5 * u_now(1) );
         F(1, 2) =  u_now(0) * ::cos( x_pre(2) + 0.5 * u_now(1) );

         // 3. state covarince prediction
         P_now = F * P_pre * F.transpose() + Q;

        //  std::cout<<"delta_theta = "<<x_now(2) - x_pre(2)<<std::endl;
         // 4. update the old value
        //  x_pre = x_now;
        //  P_pre = P_now;
         //std::cout<<"x estimated : "<<std::endl<<x_now<<std::endl;
        //  std::cout<<" prediction end "<<std::endl;
         
}
void ekf_imu_odometry::update( const double z )
{
        if( is_init == false ){
                return;
        }
        // std::cout<<" update "<<std::endl;

        // 1. measurement estimate
        H << 0, 0, 1;
        double h = H * x_now - x_pre(2);
        // std::cout<<"h = "<<h<<std::endl;
        // std::cout<<"z = "<<z<<std::endl;

        // 2. measurement error
        //DataType error = z - h;
        double error = z - h;                                                         //观测值与预测值误差
        // std::cout<<"error = "<<error<<std::endl;

        // 3. Kalman Gain
        double K_tmp = H * P_now * H.transpose() + R;
        Eigen::Matrix<float,3,1> K = P_now * H.transpose() * ( 1 / K_tmp );

        // std::cout<<"K * error = "<<std::endl<<K * error<<std::endl;
        // 4. state update
        x_now += K * error;

        // 5. state covarince matrix update
        P_now = ( Matrix3f::Identity() - K * H ) * P_now;

        // std::cout<<" update end "<<std::endl;
        // 6. update the old value
        x_pre = x_now;
        P_pre = P_now;
}
