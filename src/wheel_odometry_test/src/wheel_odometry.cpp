#include "wheel_odometry.h"
using namespace std;
#include <time.h>   // or  #include <ctime>

wheelOdometry::wheelOdometry(/* args */)
{
    // origin_gnss_pose = convertToEngineMatrix(258267.6360222462,4149891.751025346,0,0,0,116.72*D2R);
    // origin_gnss_pose = convertToEngineMatrix(258485.1826716051,4149792.0834634085,0,0,0,95.734458*D2R);
    // origin_gnss_pose = convertToEngineMatrix(258448.77633937128,4149794.4836998964,0,0,0,121.5076*D2R);
    origin_gnss_pose = convertToEngineMatrix(258015.2052508307,4150105.984503021,0,0,0,-352.54903*D2R);

    // 初始化EKF
    Eigen::Vector3f initial_state(0.0f, 0.0f, 0.0f);  // 初始位置和角度
    m_ekf_imu_odometry.initialize(initial_state);

    // 设置噪声参数 - 根据车辆和传感器特性调整
    m_ekf_imu_odometry.setProcessNoise(0.1, 0.01);    // 位置噪声0.1m^2, 角度噪声0.01rad^2
    m_ekf_imu_odometry.setMeasurementNoise(0.05);     // IMU角速度噪声0.05rad^2

    // 初始化angle_delta_z
    angle_delta_z = 0.0;

    std::cout << "[WheelOdometry] EKF initialized with noise parameters" << std::endl;
}

wheelOdometry::~wheelOdometry()
{
}

void wheelOdometry::setEKFNoiseParameters(double pos_noise, double angle_noise, double imu_noise)
{
    std::cout << "[WheelOdometry] Updating EKF noise parameters: pos=" << pos_noise
              << ", angle=" << angle_noise << ", imu=" << imu_noise << std::endl;

    m_ekf_imu_odometry.setProcessNoise(pos_noise, angle_noise);
    m_ekf_imu_odometry.setMeasurementNoise(imu_noise);
}

void wheelOdometry::resetEKF(const Eigen::Vector3f& initial_state)
{
    std::cout << "[WheelOdometry] Resetting EKF with initial state: ["
              << initial_state(0) << ", " << initial_state(1) << ", " << initial_state(2) << "]" << std::endl;

    m_ekf_imu_odometry.initialize(initial_state);
}

Eigen::Matrix4d wheelOdometry::convertToEngineMatrix(double x, double y, double z, double roll, double pitch, double yaw) 
{
    Eigen::Matrix3d R_x, R_y, R_z; // Rotation matrices
    R_x = Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());
    R_y = Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY());
    R_z = Eigen::AngleAxisd(yaw, Eigen::Vector3d::UnitZ());
    Eigen::Matrix4d transform = Eigen::Matrix4d::Identity();
    transform.block<3,3>(0,0) = R_z * R_y * R_x; // Rotation
    transform.block<3,1>(0,3) = Eigen::Vector3d(x, y, z); // Translation
    Eigen::Matrix4d engineMatrix = transform.cast<double>();
    return engineMatrix;
}
Vector6d wheelOdometry::convertFromEngineMatrix(Eigen::Matrix4d transform) 
{
    // 旋转部分（3x3子矩阵）  
    Eigen::Matrix3d rotation = transform.block<3, 3>(0, 0); 
    Vector6d eulerAngles;  
    eulerAngles(0) = transform(0, 3);
    eulerAngles(1) = transform(1, 3);
    eulerAngles(2) = transform(2, 3);
    //roll (x-axis rotation)  
    eulerAngles(3) = std::atan2(rotation(2, 1), rotation(2, 2));  
    //pitch (y-axis rotation)  
    double cosRoll = std::sqrt(rotation(2, 1) * rotation(2, 1) + rotation(2, 2) * rotation(2, 2));  
    eulerAngles(4) = std::atan2(-rotation(2, 0), cosRoll);  
    //yaw (z-axis rotation)  
    eulerAngles(5) = std::atan2(rotation(1, 0), rotation(0, 0));  
    return eulerAngles;
}
void wheelOdometry::fusion_callback(const perception_msgs::PerceptionLocalization::ConstPtr& input)
{
    double x_ = rtk_x-input->position_x;
    double y_ = rtk_y-input->position_y;
    double delta = sqrt(x_*x_+y_*y_);
    std::cout<<"**********delta******"<<delta<<std::endl;
}
void wheelOdometry::imu_callback(const perception_msgs::PerceptionLocalization::ConstPtr& input)
{
    sensor_msgs::Imu thisImu;
    thisImu.header.stamp = input->header.stamp;
    thisImu.angular_velocity.x = input->angular_velocity_x;                                        
    thisImu.angular_velocity.y = input->angular_velocity_y;                                       
    thisImu.angular_velocity.z = input->angular_velocity_z;                                        
    thisImu.linear_acceleration.x = input->accel_x;                                       
    thisImu.linear_acceleration.y = input->accel_y;                                        
    thisImu.linear_acceleration.x = input->accel_x; 

    angle_delta_z = input->angular_velocity_z;      

  
    rtk_x = input->position_x;
    rtk_y = input->position_y;
    Eigen::Matrix4d current_gnss_pose = convertToEngineMatrix(input->position_x,input->position_y,0,0,0,(input->yaw) * D2R);
    Eigen::Matrix4d transform_pose    = origin_gnss_pose.inverse()*current_gnss_pose;
    Vector6d pose                     = convertFromEngineMatrix(transform_pose);
    nav_msgs::Odometry odom_gnss;
    odom_gnss.header.stamp          = input->header.stamp;
    odom_gnss.header.frame_id       = "map";
    odom_gnss.pose.pose.position.x  = pose(0);
    odom_gnss.pose.pose.position.y  = pose(1);
    float  gnss_angle               = pose(5); 
    odom_gnss.pose.pose.orientation = tf::createQuaternionMsgFromYaw(gnss_angle);
    gnss_odom_pub.publish(odom_gnss);    

}
/*   原车辆轮速计模型 车辆速度及方向盘转向角
void wheelOdometry::wheel_callback(const control_msgs::VehicleFdb::ConstPtr& input)
{
    static double last_time=-1;
    double now_time = input->header.stamp.toSec();
    if (last_time < 0)
    {
        last_time = now_time;
        return;
    }
    double time_=now_time-last_time;
    //ROS_INFO("input->header.stamp.toSec(): : %lf",input->header.stamp.toSec());
    //std::cout<<"-------time_: "<<time_<<std::endl;
    //double time_=0.01;
    Vector2f m_wheelData;
    float speed = input->vcu_real_speed/3.6;    //km/h -> m/s
    if(input->cur_gear!=3)
    {
        speed = - speed; 
    }
    float angle_rate = speed * tan((input->steering_wheel_angle/15)* D2R) / 2.49;   //轴距2.49米
    m_wheelData(0) = speed*time_, m_wheelData(1) = angle_rate*time_;
    //cout<<"-----------m_wheelData(1): "<<m_wheelData(1) <<endl;
    m_ekf_imu_odometry.predict(m_wheelData, time_);  // 使用正确的API，包含时间参数
    static double angle = 0;
    float angle_increase = -angle_delta_z *time_* D2R;
    //cout<<"----------angle_increase: "<<angle_increase <<endl;
    m_ekf_imu_odometry.update(angle_increase);
    // angle = angle + angle_increase;
    // cout<<"----------angle: "<<angle <<endl;
    Eigen::Vector3f predict_data = m_ekf_imu_odometry.getState();
    nav_msgs::Odometry odom;
    odom.header.stamp = input->header.stamp;
    odom.header.frame_id = "map";
    double theta = 33 * D2R ;
    double cosTheta = std::cos(theta);
    double sinTheta = std::sin(theta);  
    // double rotated_x = cosTheta*odom_gnss.pose.pose.position.x - sinTheta*odom_gnss.pose.pose.position.y;
    // double rotated_y = deltasinTheta*odom_gnss.pose.pose.position.x + cosTheta*odom_gnss.pose.pose.position.y;
    odom.pose.pose.position.x = cosTheta*predict_data(0)- sinTheta*predict_data(1);
    odom.pose.pose.position.y = sinTheta*predict_data(0)+ cosTheta*predict_data(1);
    float  odom_angle = predict_data(2);
    odom.pose.pose.orientation =tf::createQuaternionMsgFromYaw(odom_angle);
    wheel_odom_pub.publish(odom);
    last_time = now_time;

}
*/
//VehicleWheelWidth轮距
void wheelOdometry::wheel_callback(const common_msgs::SpeedStreer::ConstPtr& input)
{
    const clock_t begin_time = clock();

    double now_time = input->header.stamp.toSec();
    std::cout << "\n[WHEEL_DEBUG] ===== WHEEL CALLBACK START =====" << std::endl;
    std::cout << "[WHEEL_DEBUG] Current timestamp: " << std::fixed << std::setprecision(6) << now_time << std::endl;
    std::cout << "[WHEEL_DEBUG] Last timestamp: " << last_time << std::endl;

    if (last_time <= 0)
    {
        std::cout << "[WHEEL_DEBUG] First callback, initializing timestamp" << std::endl;
        last_time = now_time;
        return;
    }

    double time_ = now_time - last_time;
    std::cout << "[WHEEL_DEBUG] Raw time interval: " << time_ << " seconds" << std::endl;

    // 防止异常的时间间隔
    if (time_ <= 0 || time_ > 1.0) {  // 修复：允许最大1秒的时间间隔
        std::cout << "[WHEEL_DEBUG] ⚠️  Abnormal time interval detected! Using fallback: 0.01s" << std::endl;
        time_ = 0.01;  // 使用默认值作为fallback
    }
    std::cout << "[WHEEL_DEBUG] Final time interval: " << time_ << " seconds" << std::endl;
    // 原始轮速数据
    std::cout << "[WHEEL_DEBUG] Raw wheel speeds - Left: " << input->rel_speed_rear_axle_left
              << " km/h, Right: " << input->rel_speed_rear_axle_right << " km/h" << std::endl;
    std::cout << "[WHEEL_DEBUG] Current gear: " << current_gear << std::endl;

    float speed = (input->rel_speed_rear_axle_left  + input->rel_speed_rear_axle_right)*0.5;
    std::cout << "[WHEEL_DEBUG] Average speed (before gear): " << speed << " km/h" << std::endl;

    if(current_gear == 1){
        speed = - speed;
        std::cout << "[WHEEL_DEBUG] Reverse gear applied, speed: " << speed << " km/h" << std::endl;
    }else if(current_gear == 2){
        speed = 0;
        std::cout << "[WHEEL_DEBUG] Park gear applied, speed: " << speed << " km/h" << std::endl;
    }

    float angle_rate = (input->rel_speed_rear_axle_right - input->rel_speed_rear_axle_left )/VehicleWheelWidth;
    std::cout << "[WHEEL_DEBUG] Angular rate calculation:" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Speed difference: " << (input->rel_speed_rear_axle_right - input->rel_speed_rear_axle_left) << " km/h" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Vehicle width: " << VehicleWheelWidth << " m" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Raw angular rate: " << angle_rate << " (km/h)/m" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Angular rate in rad/s: " << angle_rate/3.6 << " rad/s" << std::endl;

    m_wheelData(0) = speed/3.6 * time_;  // 位移增量 (m)
    m_wheelData(1) = angle_rate/3.6 * time_;  // 角度增量 (rad)

    std::cout << "[WHEEL_DEBUG] Control input calculation:" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Speed: " << speed << " km/h = " << speed/3.6 << " m/s" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Displacement increment: " << m_wheelData(0) << " m" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Angular increment: " << m_wheelData(1) << " rad = " << m_wheelData(1)*180/3.14159 << " deg" << std::endl;

    m_ekf_imu_odometry.predict(m_wheelData, time_);
    static double angle = 0;
    // sensor_msgs::Imu ImuData;
    std::cout << "[WHEEL_DEBUG] IMU data processing:" << std::endl;
    std::cout << "[WHEEL_DEBUG]   angle_delta_z: " << angle_delta_z << " deg/s" << std::endl;
    std::cout << "[WHEEL_DEBUG]   time interval: " << time_ << " s" << std::endl;

    float angle_increase = angle_delta_z * time_ * D2R;
    std::cout << "[WHEEL_DEBUG]   angle_increase: " << angle_increase << " rad = " << angle_increase*180/3.14159 << " deg" << std::endl;
    std::cout << "[WHEEL_DEBUG]   accumulated angle before: " << angle << " rad = " << angle*180/3.14159 << " deg" << std::endl;

    m_ekf_imu_odometry.update(angle_increase);
    angle = angle + angle_increase;

    std::cout << "[WHEEL_DEBUG]   accumulated angle after: " << angle << " rad = " << angle*180/3.14159 << " deg" << std::endl;

    // double time_ = input->header.stamp.toSec()-last_time;

    // Vector2f m_wheelData;
    // float speed = (input->rel_speed_steer_axle_left + input->rel_speed_steer_axle_right +  input->rel_speed_rear_axle_left + input->rel_speed_rear_axle_right)*0.25 ;
    // float angle_rate = (input->rel_speed_steer_axle_right +input->rel_speed_rear_axle_right - input->rel_speed_steer_axle_left-input->rel_speed_rear_axle_left )/VehicleWheelWidth * 0.5;
    // m_wheelData(0) = speed/3.6 *time_ ,m_wheelData(1) = angle_rate/3.6* time_;
    // m_ekf_imu_odometry.predict(m_wheelData);
    // static double angle = 0;
    // sensor_msgs::Imu ImuData;
    // float angle_increase = angle_delta_z *time_* D2R;
    // m_ekf_imu_odometry.update(angle_increase);
    // angle = angle + angle_increase;

    // if(m_ImuData.SyncData(ImuData,input->header.stamp.toSec())){
    //     float angle_increase = ImuData.angular_velocity.z *0.01;
    //     m_ekf_imu_odometry.update(angle_increase);
    //     angle = angle + angle_increase;
    // }else{
    //     ROS_WARN("no searched closed imu data");
    // }
    Eigen::Vector3f predict_data = m_ekf_imu_odometry.getState();

    std::cout << "[WHEEL_DEBUG] EKF output state:" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Position: x=" << predict_data(0) << " m, y=" << predict_data(1) << " m" << std::endl;
    std::cout << "[WHEEL_DEBUG]   Orientation: theta=" << predict_data(2) << " rad = " << predict_data(2)*180/3.14159 << " deg" << std::endl;

    float now_y = predict_data(1);
    float delta_y = now_y - last_y;
    std::cout << "[WHEEL_DEBUG] Y-coordinate tracking: last_y=" << last_y << ", now_y=" << now_y << ", delta_y=" << delta_y << std::endl;
    last_y = predict_data(1);

    std::cout << "[WHEEL_DEBUG] Will update last_time at end of callback" << std::endl;

    nav_msgs::Odometry odom;
    odom.header.stamp = input->header.stamp;
    odom.header.frame_id = "map";
    odom.pose.pose.position.x = predict_data(0);
    odom.pose.pose.position.y = predict_data(1);
    float  odom_angle_ = predict_data(2);
    odom.pose.pose.orientation =tf::createQuaternionMsgFromYaw(odom_angle_);
    wheel_odom_pub_.publish(odom);
    
    geometry_msgs::PoseStamped pose_stamped;
    pose_stamped.header.stamp = input->header.stamp;;
    pose_stamped.header.frame_id = "map";
    pose_stamped.pose.position.x = predict_data(0);
    pose_stamped.pose.position.y = predict_data(1);
    float  odom_angle = predict_data(2);
    pose_stamped.pose.orientation = tf::createQuaternionMsgFromYaw(odom_angle);
    wheel_odom_pub.publish(pose_stamped);
    
    pose_now =convertToEngineMatrix(predict_data(0),predict_data(1),0,0,0,predict_data(2));
    Eigen::Matrix4d R = pose_last.inverse() * pose_now;
    std::cout << "[WHEEL_DEBUG] Pose transformation matrix:" << std::endl << R << std::endl;
    pose_last =convertToEngineMatrix(predict_data(0),predict_data(1),0,0,0,predict_data(2));

    // 在最后更新时间戳
    last_time = now_time;
    std::cout << "[WHEEL_DEBUG] Updated last_time to: " << last_time << std::endl;

    float seconds = float(clock( ) - begin_time) / 1000;   //最小精度到ms
    std::cout << "[WHEEL_DEBUG] Processing time: " << seconds << " ms" << std::endl;
    std::cout << "[WHEEL_DEBUG] ===== WHEEL CALLBACK END =====\n" << std::endl;
}

void wheelOdometry::gear_callback(const control_msgs::Jinlong_Control_ModeFlag::ConstPtr& input)
{ 
    current_gear = input->vehicle_current_gear;
}

void wheelOdometry::run()
{
    ros::NodeHandle private_nh("~");
    wheel_odom_pub                     = private_nh.advertise<geometry_msgs::PoseStamped>("/wheel_odometry", 5);
    wheel_odom_pub_                     = private_nh.advertise<nav_msgs::Odometry>("/wheel_odometry_rviz", 5);
    gnss_odom_pub                   = private_nh.advertise<nav_msgs::Odometry>("/gnss_odometry", 10);
    ros::Subscriber imu_sub      = private_nh.subscribe("/cicv_location_rtk", 10, &wheelOdometry::imu_callback,this);
    ros::Subscriber wheel_sub  = private_nh.subscribe("/car_wheel", 10, &wheelOdometry::wheel_callback,this);
    // ros::Subscriber fusion_sub  = private_nh.subscribe("/cicv_location_fusion_", 100, &wheelOdometry::fusion_callback,this);
    ros::Subscriber gear_sub  = private_nh.subscribe("/jinlong_flag_pub", 2, &wheelOdometry::gear_callback,this);
    ros::spin();
}
// int main(int argc, char** argv)
// {
//     ros::init(argc, argv, "wheel_odometry");
//     wheelOdometry wheelOdometry;
//     wheelOdometry.run();
//     return 0;
// }
