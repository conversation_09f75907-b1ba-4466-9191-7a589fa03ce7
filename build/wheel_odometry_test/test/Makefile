# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/loc/src

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/loc/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/loc/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/loc/build/CMakeFiles /home/<USER>/loc/build/wheel_odometry_test/test/CMakeFiles/progress.marks
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/loc/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/loc/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/rule:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/rule
.PHONY : wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/rule

# Convenience name for target.
test_ekf_runtime_simple: wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/rule

.PHONY : test_ekf_runtime_simple

# fast build rule for target.
test_ekf_runtime_simple/fast:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build
.PHONY : test_ekf_runtime_simple/fast

# Convenience name for target.
wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/rule:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/rule
.PHONY : wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/rule

# Convenience name for target.
test_wheel_odometry_runtime: wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/rule

.PHONY : test_wheel_odometry_runtime

# fast build rule for target.
test_wheel_odometry_runtime/fast:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build
.PHONY : test_wheel_odometry_runtime/fast

# Convenience name for target.
wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/rule:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/rule
.PHONY : wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/rule

# Convenience name for target.
test_wheel_odometry_completeness: wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/rule

.PHONY : test_wheel_odometry_completeness

# fast build rule for target.
test_wheel_odometry_completeness/fast:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build
.PHONY : test_wheel_odometry_completeness/fast

# Convenience name for target.
wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/rule:
	cd /home/<USER>/loc/build && $(MAKE) -f CMakeFiles/Makefile2 wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/rule
.PHONY : wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/rule

# Convenience name for target.
test_ekf_comprehensive: wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/rule

.PHONY : test_ekf_comprehensive

# fast build rule for target.
test_ekf_comprehensive/fast:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build
.PHONY : test_ekf_comprehensive/fast

__/src/ekf_imu_odometry.o: __/src/ekf_imu_odometry.cpp.o

.PHONY : __/src/ekf_imu_odometry.o

# target to build an object file
__/src/ekf_imu_odometry.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/__/src/ekf_imu_odometry.cpp.o
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/ekf_imu_odometry.cpp.o
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/ekf_imu_odometry.cpp.o
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/__/src/ekf_imu_odometry.cpp.o
.PHONY : __/src/ekf_imu_odometry.cpp.o

__/src/ekf_imu_odometry.i: __/src/ekf_imu_odometry.cpp.i

.PHONY : __/src/ekf_imu_odometry.i

# target to preprocess a source file
__/src/ekf_imu_odometry.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/__/src/ekf_imu_odometry.cpp.i
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/ekf_imu_odometry.cpp.i
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/ekf_imu_odometry.cpp.i
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/__/src/ekf_imu_odometry.cpp.i
.PHONY : __/src/ekf_imu_odometry.cpp.i

__/src/ekf_imu_odometry.s: __/src/ekf_imu_odometry.cpp.s

.PHONY : __/src/ekf_imu_odometry.s

# target to generate assembly for a file
__/src/ekf_imu_odometry.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/__/src/ekf_imu_odometry.cpp.s
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/ekf_imu_odometry.cpp.s
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/ekf_imu_odometry.cpp.s
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/__/src/ekf_imu_odometry.cpp.s
.PHONY : __/src/ekf_imu_odometry.cpp.s

__/src/wheel_odometry.o: __/src/wheel_odometry.cpp.o

.PHONY : __/src/wheel_odometry.o

# target to build an object file
__/src/wheel_odometry.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/wheel_odometry.cpp.o
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/wheel_odometry.cpp.o
.PHONY : __/src/wheel_odometry.cpp.o

__/src/wheel_odometry.i: __/src/wheel_odometry.cpp.i

.PHONY : __/src/wheel_odometry.i

# target to preprocess a source file
__/src/wheel_odometry.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/wheel_odometry.cpp.i
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/wheel_odometry.cpp.i
.PHONY : __/src/wheel_odometry.cpp.i

__/src/wheel_odometry.s: __/src/wheel_odometry.cpp.s

.PHONY : __/src/wheel_odometry.s

# target to generate assembly for a file
__/src/wheel_odometry.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/__/src/wheel_odometry.cpp.s
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/__/src/wheel_odometry.cpp.s
.PHONY : __/src/wheel_odometry.cpp.s

test_ekf_comprehensive.o: test_ekf_comprehensive.cpp.o

.PHONY : test_ekf_comprehensive.o

# target to build an object file
test_ekf_comprehensive.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/test_ekf_comprehensive.cpp.o
.PHONY : test_ekf_comprehensive.cpp.o

test_ekf_comprehensive.i: test_ekf_comprehensive.cpp.i

.PHONY : test_ekf_comprehensive.i

# target to preprocess a source file
test_ekf_comprehensive.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/test_ekf_comprehensive.cpp.i
.PHONY : test_ekf_comprehensive.cpp.i

test_ekf_comprehensive.s: test_ekf_comprehensive.cpp.s

.PHONY : test_ekf_comprehensive.s

# target to generate assembly for a file
test_ekf_comprehensive.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_comprehensive.dir/test_ekf_comprehensive.cpp.s
.PHONY : test_ekf_comprehensive.cpp.s

test_ekf_runtime_simple.o: test_ekf_runtime_simple.cpp.o

.PHONY : test_ekf_runtime_simple.o

# target to build an object file
test_ekf_runtime_simple.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/test_ekf_runtime_simple.cpp.o
.PHONY : test_ekf_runtime_simple.cpp.o

test_ekf_runtime_simple.i: test_ekf_runtime_simple.cpp.i

.PHONY : test_ekf_runtime_simple.i

# target to preprocess a source file
test_ekf_runtime_simple.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/test_ekf_runtime_simple.cpp.i
.PHONY : test_ekf_runtime_simple.cpp.i

test_ekf_runtime_simple.s: test_ekf_runtime_simple.cpp.s

.PHONY : test_ekf_runtime_simple.s

# target to generate assembly for a file
test_ekf_runtime_simple.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/build.make wheel_odometry_test/test/CMakeFiles/test_ekf_runtime_simple.dir/test_ekf_runtime_simple.cpp.s
.PHONY : test_ekf_runtime_simple.cpp.s

test_wheel_odometry_completeness.o: test_wheel_odometry_completeness.cpp.o

.PHONY : test_wheel_odometry_completeness.o

# target to build an object file
test_wheel_odometry_completeness.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/test_wheel_odometry_completeness.cpp.o
.PHONY : test_wheel_odometry_completeness.cpp.o

test_wheel_odometry_completeness.i: test_wheel_odometry_completeness.cpp.i

.PHONY : test_wheel_odometry_completeness.i

# target to preprocess a source file
test_wheel_odometry_completeness.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/test_wheel_odometry_completeness.cpp.i
.PHONY : test_wheel_odometry_completeness.cpp.i

test_wheel_odometry_completeness.s: test_wheel_odometry_completeness.cpp.s

.PHONY : test_wheel_odometry_completeness.s

# target to generate assembly for a file
test_wheel_odometry_completeness.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_completeness.dir/test_wheel_odometry_completeness.cpp.s
.PHONY : test_wheel_odometry_completeness.cpp.s

test_wheel_odometry_runtime.o: test_wheel_odometry_runtime.cpp.o

.PHONY : test_wheel_odometry_runtime.o

# target to build an object file
test_wheel_odometry_runtime.cpp.o:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/test_wheel_odometry_runtime.cpp.o
.PHONY : test_wheel_odometry_runtime.cpp.o

test_wheel_odometry_runtime.i: test_wheel_odometry_runtime.cpp.i

.PHONY : test_wheel_odometry_runtime.i

# target to preprocess a source file
test_wheel_odometry_runtime.cpp.i:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/test_wheel_odometry_runtime.cpp.i
.PHONY : test_wheel_odometry_runtime.cpp.i

test_wheel_odometry_runtime.s: test_wheel_odometry_runtime.cpp.s

.PHONY : test_wheel_odometry_runtime.s

# target to generate assembly for a file
test_wheel_odometry_runtime.cpp.s:
	cd /home/<USER>/loc/build && $(MAKE) -f wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/build.make wheel_odometry_test/test/CMakeFiles/test_wheel_odometry_runtime.dir/test_wheel_odometry_runtime.cpp.s
.PHONY : test_wheel_odometry_runtime.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... test"
	@echo "... test_ekf_runtime_simple"
	@echo "... test_wheel_odometry_runtime"
	@echo "... test_wheel_odometry_completeness"
	@echo "... test_ekf_comprehensive"
	@echo "... __/src/ekf_imu_odometry.o"
	@echo "... __/src/ekf_imu_odometry.i"
	@echo "... __/src/ekf_imu_odometry.s"
	@echo "... __/src/wheel_odometry.o"
	@echo "... __/src/wheel_odometry.i"
	@echo "... __/src/wheel_odometry.s"
	@echo "... test_ekf_comprehensive.o"
	@echo "... test_ekf_comprehensive.i"
	@echo "... test_ekf_comprehensive.s"
	@echo "... test_ekf_runtime_simple.o"
	@echo "... test_ekf_runtime_simple.i"
	@echo "... test_ekf_runtime_simple.s"
	@echo "... test_wheel_odometry_completeness.o"
	@echo "... test_wheel_odometry_completeness.i"
	@echo "... test_wheel_odometry_completeness.s"
	@echo "... test_wheel_odometry_runtime.o"
	@echo "... test_wheel_odometry_runtime.i"
	@echo "... test_wheel_odometry_runtime.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/loc/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

